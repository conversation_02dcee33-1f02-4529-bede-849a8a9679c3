-- Performance indexes for the faafo career platform

-- User-related indexes
CREATE INDEX IF NOT EXISTS idx_user_email ON User(email);
CREATE INDEX IF NOT EXISTS idx_user_created_at ON User(createdAt);
CREATE INDEX IF NOT EXISTS idx_user_updated_at ON User(updatedAt);

-- Assessment indexes
CREATE INDEX IF NOT EXISTS idx_assessment_user_status ON Assessment(userId, status);
CREATE INDEX IF NOT EXISTS idx_assessment_created_at ON Assessment(createdAt);
CREATE INDEX IF NOT EXISTS idx_assessment_completed_at ON Assessment(completedAt);
CREATE INDEX IF NOT EXISTS idx_assessment_user_completed ON Assessment(userId, completedAt) WHERE status = 'COMPLETED';

-- Assessment response indexes
CREATE INDEX IF NOT EXISTS idx_assessment_response_assessment ON AssessmentResponse(assessmentId);
CREATE INDEX IF NOT EXISTS idx_assessment_response_question ON AssessmentResponse(questionKey);

-- Learning resource indexes
CREATE INDEX IF NOT EXISTS idx_learning_resource_category_level ON LearningResource(category, skillLevel);
CREATE INDEX IF NOT EXISTS idx_learning_resource_active ON LearningResource(isActive);
CREATE INDEX IF NOT EXISTS idx_learning_resource_type ON LearningResource(type);
CREATE INDEX IF NOT EXISTS idx_learning_resource_created_at ON LearningResource(createdAt);
CREATE INDEX IF NOT EXISTS idx_learning_resource_rating ON LearningResource(averageRating);

-- User learning progress indexes
CREATE INDEX IF NOT EXISTS idx_user_learning_progress_user_status ON UserLearningProgress(userId, status);
CREATE INDEX IF NOT EXISTS idx_user_learning_progress_resource ON UserLearningProgress(resourceId);
CREATE INDEX IF NOT EXISTS idx_user_learning_progress_completed ON UserLearningProgress(completedAt) WHERE status = 'COMPLETED';
CREATE INDEX IF NOT EXISTS idx_user_learning_progress_user_resource ON UserLearningProgress(userId, resourceId);

-- Forum indexes
CREATE INDEX IF NOT EXISTS idx_forum_post_category_created ON ForumPost(categoryId, createdAt);
CREATE INDEX IF NOT EXISTS idx_forum_post_author_created ON ForumPost(authorId, createdAt);
CREATE INDEX IF NOT EXISTS idx_forum_post_active ON ForumPost(isActive);
CREATE INDEX IF NOT EXISTS idx_forum_post_pinned ON ForumPost(isPinned, createdAt);

-- Forum comment indexes
CREATE INDEX IF NOT EXISTS idx_forum_comment_post_created ON ForumComment(postId, createdAt);
CREATE INDEX IF NOT EXISTS idx_forum_comment_author ON ForumComment(authorId);
CREATE INDEX IF NOT EXISTS idx_forum_comment_active ON ForumComment(isActive);

-- Career path indexes
CREATE INDEX IF NOT EXISTS idx_career_path_active ON CareerPath(isActive);
CREATE INDEX IF NOT EXISTS idx_career_path_created_at ON CareerPath(createdAt);
CREATE INDEX IF NOT EXISTS idx_career_path_slug ON CareerPath(slug);

-- Skill indexes
CREATE INDEX IF NOT EXISTS idx_skill_category ON Skill(category);
CREATE INDEX IF NOT EXISTS idx_skill_name ON Skill(name);

-- Learning path indexes
CREATE INDEX IF NOT EXISTS idx_learning_path_active ON LearningPath(isActive);
CREATE INDEX IF NOT EXISTS idx_learning_path_category ON LearningPath(category);
CREATE INDEX IF NOT EXISTS idx_learning_path_difficulty ON LearningPath(difficulty);
CREATE INDEX IF NOT EXISTS idx_learning_path_created_at ON LearningPath(createdAt);
CREATE INDEX IF NOT EXISTS idx_learning_path_slug ON LearningPath(slug);

-- Learning path step indexes
CREATE INDEX IF NOT EXISTS idx_learning_path_step_path_order ON LearningPathStep(learningPathId, stepOrder);
CREATE INDEX IF NOT EXISTS idx_learning_path_step_resource ON LearningPathStep(resourceId);
CREATE INDEX IF NOT EXISTS idx_learning_path_step_required ON LearningPathStep(isRequired);

-- User learning path indexes
CREATE INDEX IF NOT EXISTS idx_user_learning_path_user_status ON UserLearningPath(userId, status);
CREATE INDEX IF NOT EXISTS idx_user_learning_path_path ON UserLearningPath(learningPathId);
CREATE INDEX IF NOT EXISTS idx_user_learning_path_completed ON UserLearningPath(completedAt) WHERE status = 'COMPLETED';
CREATE INDEX IF NOT EXISTS idx_user_learning_path_last_accessed ON UserLearningPath(lastAccessedAt);

-- User learning path progress indexes
CREATE INDEX IF NOT EXISTS idx_user_learning_path_progress_user_status ON UserLearningPathProgress(userId, status);
CREATE INDEX IF NOT EXISTS idx_user_learning_path_progress_step ON UserLearningPathProgress(stepId);
CREATE INDEX IF NOT EXISTS idx_user_learning_path_progress_user_path ON UserLearningPathProgress(userLearningPathId);
CREATE INDEX IF NOT EXISTS idx_user_learning_path_progress_completed ON UserLearningPathProgress(completedAt) WHERE status = 'COMPLETED';

-- User skill progress indexes
CREATE INDEX IF NOT EXISTS idx_user_skill_progress_user_level ON UserSkillProgress(userId, currentLevel);
CREATE INDEX IF NOT EXISTS idx_user_skill_progress_skill ON UserSkillProgress(skillId);
CREATE INDEX IF NOT EXISTS idx_user_skill_progress_last_practiced ON UserSkillProgress(lastPracticed);
CREATE INDEX IF NOT EXISTS idx_user_skill_progress_points ON UserSkillProgress(progressPoints);

-- Learning analytics indexes
CREATE INDEX IF NOT EXISTS idx_learning_analytics_user_date ON LearningAnalytics(userId, date);
CREATE INDEX IF NOT EXISTS idx_learning_analytics_date ON LearningAnalytics(date);
CREATE INDEX IF NOT EXISTS idx_learning_analytics_user_recent ON LearningAnalytics(userId, date DESC);

-- Resource rating indexes
CREATE INDEX IF NOT EXISTS idx_resource_rating_resource ON ResourceRating(resourceId);
CREATE INDEX IF NOT EXISTS idx_resource_rating_user ON ResourceRating(userId);
CREATE INDEX IF NOT EXISTS idx_resource_rating_resource_rating ON ResourceRating(resourceId, rating);

-- User goal indexes
CREATE INDEX IF NOT EXISTS idx_user_goal_user_status ON UserGoal(userId, status);
CREATE INDEX IF NOT EXISTS idx_user_goal_target_date ON UserGoal(targetDate);
CREATE INDEX IF NOT EXISTS idx_user_goal_completed ON UserGoal(completedAt) WHERE status = 'COMPLETED';

-- User achievement indexes
CREATE INDEX IF NOT EXISTS idx_user_achievement_user ON UserAchievement(userId);
CREATE INDEX IF NOT EXISTS idx_user_achievement_type ON UserAchievement(achievementType);
CREATE INDEX IF NOT EXISTS idx_user_achievement_earned_at ON UserAchievement(earnedAt);

-- Financial goal indexes
CREATE INDEX IF NOT EXISTS idx_financial_goal_user_status ON FinancialGoal(userId, status);
CREATE INDEX IF NOT EXISTS idx_financial_goal_target_date ON FinancialGoal(targetDate);
CREATE INDEX IF NOT EXISTS idx_financial_goal_category ON FinancialGoal(category);

-- Expense indexes
CREATE INDEX IF NOT EXISTS idx_expense_user_date ON Expense(userId, date);
CREATE INDEX IF NOT EXISTS idx_expense_category ON Expense(category);
CREATE INDEX IF NOT EXISTS idx_expense_date ON Expense(date);

-- Income indexes
CREATE INDEX IF NOT EXISTS idx_income_user_date ON Income(userId, date);
CREATE INDEX IF NOT EXISTS idx_income_source ON Income(source);
CREATE INDEX IF NOT EXISTS idx_income_date ON Income(date);

-- Suggestion rule indexes
CREATE INDEX IF NOT EXISTS idx_suggestion_rule_career_path ON SuggestionRule(careerPathId);
CREATE INDEX IF NOT EXISTS idx_suggestion_rule_active ON SuggestionRule(isActive);

-- NextAuth indexes
CREATE INDEX IF NOT EXISTS idx_account_user_id ON Account(userId);
CREATE INDEX IF NOT EXISTS idx_account_provider ON Account(provider, providerAccountId);
CREATE INDEX IF NOT EXISTS idx_session_user_id ON Session(userId);
CREATE INDEX IF NOT EXISTS idx_session_token ON Session(sessionToken);
CREATE INDEX IF NOT EXISTS idx_verification_token_identifier ON VerificationToken(identifier);
CREATE INDEX IF NOT EXISTS idx_verification_token_token ON VerificationToken(token);

-- Composite indexes for common query patterns
CREATE INDEX IF NOT EXISTS idx_user_learning_progress_user_resource_status ON UserLearningProgress(userId, resourceId, status);
CREATE INDEX IF NOT EXISTS idx_forum_post_category_active_created ON ForumPost(categoryId, isActive, createdAt);
CREATE INDEX IF NOT EXISTS idx_learning_resource_category_active_rating ON LearningResource(category, isActive, averageRating);
CREATE INDEX IF NOT EXISTS idx_user_learning_path_user_path_status ON UserLearningPath(userId, learningPathId, status);
CREATE INDEX IF NOT EXISTS idx_assessment_user_status_completed ON Assessment(userId, status, completedAt);

-- Full-text search indexes (if supported by the database)
-- Note: These might need to be adjusted based on the specific database engine
-- CREATE INDEX IF NOT EXISTS idx_learning_resource_search ON LearningResource USING gin(to_tsvector('english', title || ' ' || description));
-- CREATE INDEX IF NOT EXISTS idx_career_path_search ON CareerPath USING gin(to_tsvector('english', name || ' ' || overview));
-- CREATE INDEX IF NOT EXISTS idx_forum_post_search ON ForumPost USING gin(to_tsvector('english', title || ' ' || content));

-- Partial indexes for better performance on filtered queries
CREATE INDEX IF NOT EXISTS idx_user_learning_progress_active_completed ON UserLearningProgress(userId, completedAt) 
  WHERE status = 'COMPLETED';

CREATE INDEX IF NOT EXISTS idx_learning_resource_active_recent ON LearningResource(createdAt) 
  WHERE isActive = true;

CREATE INDEX IF NOT EXISTS idx_forum_post_active_recent ON ForumPost(createdAt) 
  WHERE isActive = true;

CREATE INDEX IF NOT EXISTS idx_user_learning_path_active_progress ON UserLearningPath(userId, lastAccessedAt) 
  WHERE status IN ('IN_PROGRESS', 'NOT_STARTED');

-- Covering indexes for frequently accessed data
CREATE INDEX IF NOT EXISTS idx_user_learning_progress_covering ON UserLearningProgress(userId, resourceId) 
  INCLUDE (status, startedAt, completedAt, progressPercent);

CREATE INDEX IF NOT EXISTS idx_learning_resource_covering ON LearningResource(category, isActive) 
  INCLUDE (title, type, skillLevel, averageRating, duration);

-- Indexes for analytics and reporting queries
CREATE INDEX IF NOT EXISTS idx_learning_analytics_monthly ON LearningAnalytics(date, userId) 
  WHERE date >= date('now', '-30 days');

CREATE INDEX IF NOT EXISTS idx_user_skill_progress_recent ON UserSkillProgress(userId, lastPracticed) 
  WHERE lastPracticed >= date('now', '-90 days');

-- Performance monitoring indexes
CREATE INDEX IF NOT EXISTS idx_user_learning_progress_performance ON UserLearningProgress(resourceId, status, completedAt);
CREATE INDEX IF NOT EXISTS idx_learning_path_performance ON LearningPath(category, difficulty, isActive);
CREATE INDEX IF NOT EXISTS idx_user_engagement ON UserLearningPath(userId, lastAccessedAt, status);
