��� �c�   "���   @                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             SQLite format 3   @     +   @           +                                                 + .zp   �    8���k�
L�b���lTW �!�                          �r�3tableForumPostForumPostCREATE TABLE "ForumPost" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "title" TEXT NOT NULL,
    "content" TEXT NOT NULL,
    "authorId" TEXT NOT NULL,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "ForumPost_authorId_fkey" FOREIGN KEY ("authorId") REFERENCES "User" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
)1E indexsqlite_autoindex_ForumPost_1ForumPost� ))�;tableSuggestionRuleSuggestionRuleCREATE TABLE "SuggestionRule" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "careerPathId" TEXT NOT NULL,
    "questionKey" TEXT NOT NULL,
    "answerValue" JSONB NOT NULL,
    "weight" REAL NOT NULL DEFAULT 1.0,
    "notes" TEXT,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    CONSTRAINT "SuggestionRule_careerPathId_fkey" FOREIGN KEY ("careerPathId") REFERENCES "CareerPath" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);O) indexsqlite_autoindex_SuggestionRule_1SuggestionRule�U
�}tableIndustryIndustryCREATE TABLE "Industry" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "name" TEXT NOT NULL,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL
)/C indexsqlite_autoindex_Industry_1Industry�y�QtableSkillSkillCREATE TABLE "Skill" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "category" TEXT,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL
))= indexsqlite_autoindex_Skill_1Skill
�	!!�tableCareerPathCareerPath
CREATE TABLE "CareerPath" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "name" TEXT NOT NULL,
    "slug" TEXT NOT NULL,
    "overview" TEXT NOT NULL,
    "pros" TEXT NOT NULL,
    "cons" TEXT NOT NULL,
    "actionableSteps" JSONB NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL
)3
G! indexsqlite_autoindex_CareerPath_1CareerPath�211�tableAssessmentResponseAssessmentResponseCREATE TABLE "AssessmentResponse" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "assessmentId" TEXT NOT NULL,
    "questionKey" TEXT NOT NULL,
    "answerValue" JSONB NOT NULL,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "AssessmentResponse_assessmentId_fkey" FOREIGN KEY ("assessmentId") REFERENCES "Assessment" ("id") ON DELETE CASCADE ON UPDATE CASCADE
)CW1 indexsqlite_autoindex_AssessmentResponse_1AssessmentResponse	�W!!�ytableAssessmentAssessmentCREATE TABLE "Assessment" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "userId" TEXT NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'IN_PROGRESS',
    "currentStep" INTEGER NOT NULL DEFAULT 0,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    "completedAt" DATETIME,
    CONSTRAINT "Assessment_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
)3G! indexsqlite_autoindex_Assessment_1Assessment�;�MtableProfileProfileCREATE TABLE "Profile" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "userId" TEXT NOT NULL,
    "bio" TEXT,
    "profilePictureUrl" TEXT,
    "socialMediaLinks" JSONB,
    CONSTRAINT "Profile_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
)-A indexsqlite_autoindex_Profile_1Profile�?�atableUserUserCREATE TABLE "User" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "name" TEXT,
    "email" TEXT NOT NULL,
    "emailVerified" DATETIME,
    "image" TEXT,
    "password" TEXT NOT NULL,
    "passwordResetToken" TEXT,
    "passwordResetExpires" DATETIME,
    "failedLoginAttempts" INTEGER NOT NULL DEFAULT 0,
    "lockedUntil" DATETIME,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL
)'; indexsqlite_autoindex_User   %%      "���   8
   A �*�$
�
J����
�
g	�	)��>�+��A                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             �;;[G�aindex_CareerPathToLearningResource_AB_unique_CareerPathToLearningResource@CREATE UNIQUE INDEX "_CareerPathToLearningResource_AB_unique" ON "_CareerPathToLearningResource"("A", "B")�:G7�%index_CareerPathToIndustry_B_index_CareerPathToIndustry?CREATE INDEX "_CareerPathToIndustry_B_index" ON "_CareerPathToIndustry"("B")�9K7�Aindex_CareerPathToIndustry_AB_unique_CareerPathToIndustry>CREATE UNIQUE INDEX "_CareerPathToIndustry_AB_unique" ON "_CareerPathToIndustry"("A", "B")8A1�index_CareerPathToSkill_B_index_CareerPathToSkill=CREATE INDEX "_CareerPathToSkill_B_index" ON "_CareerPathToSkill"("B")�7E1�5index_CareerPathToSkill_AB_unique_CareerPathToSkill<CREATE UNIQUE INDEX "_CareerPathToSkill_AB_unique" ON "_CareerPathToSkill"("A", "B")�%6U)�YindexResourceRating_userId_resourceId_keyResourceRating;CREATE UNIQUE INDEX "ResourceRating_userId_resourceId_key" ON "ResourceRating"("userId", "resourceId")�=5a5�qindexUserLearningProgress_userId_resourceId_keyUserLearningProgress:CREATE UNIQUE INDEX "UserLearningProgress_userId_resourceId_key" ON "UserLearningProgress"("userId", "resourceId")� 4=-�#indexLearningResource_url_keyLearningResource9CREATE UNIQUE INDEX "LearningResource_url_key" ON "LearningResource"("url")�.3Y/�aindexVerificationToken_identifier_token_keyVerificationToken7CREATE UNIQUE INDEX "VerificationToken_identifier_token_key" ON "VerificationToken"("identifier", "token")�
2C/�/indexVerificationToken_token_keyVerificationToken6CREATE UNIQUE INDEX "VerificationToken_token_key" ON "VerificationToken"("token")w1=�#indexSession_sessionToken_keySession5CREATE UNIQUE INDEX "Session_sessionToken_key" ON "Session"("sessionToken")�$0Y�aindexAccount_provider_providerAccountId_keyAccount4CREATE UNIQUE INDEX "Account_provider_providerAccountId_key" ON "Account"("provider", "providerAccountId")u/9#�indexFreedomFund_userId_keyFreedomFund3CREATE UNIQUE INDEX "FreedomFund_userId_key" ON "FreedomFund"("userId")�.K)�1indexSuggestionRule_careerPathId_idxSuggestionRule2CREATE INDEX "SuggestionRule_careerPathId_idx" ON "SuggestionRule"("careerPathId")c-/�indexIndustry_name_keyIndustry1CREATE UNIQUE INDEX "Industry_name_key" ON "Industry"("name")V,){indexSkill_name_keySkill0CREATE UNIQUE INDEX "Skill_name_key" ON "Skill"("name")k+3!�indexCareerPath_slug_keyCareerPath/CREATE UNIQUE INDEX "CareerPath_slug_key" ON "CareerPath"("slug")k*3!�indexCareerPath_name_keyCareerPath.CREATE UNIQUE INDEX "CareerPath_name_key" ON "CareerPath"("name")�)S1�AindexAssessmentResponse_assessmentId_idxAssessmentResponse-CREATE INDEX "AssessmentResponse_assessmentId_idx" ON "AssessmentResponse"("assessmentId")e(1�indexProfile_userId_keyProfile,CREATE UNIQUE INDEX "Profile_userId_key" ON "Profile"("userId")}'C�/indexUser_passwordResetToken_keyUser+CREATE UNIQUE INDEX "User_passwordResetToken_key" ON "User"("passwordResetToken")U&){indexUser_email_keyUser*CREATE UNIQUE INDEX "User_email_key" ON "User"("email")"���   
y 
 	���PB,!��
�
�
�
�
�
�       �                                                                                                                                                                       ?S- indexsqlite_autoindex_LearningResource_1LearningResource �.//�tableVerificationTokenVerificationTokenCREATE TABLE "VerificationToken" (
    "identifier" TEXT NOT NULL,
    "token" TEXT NOT NULL,
    "expires" DATETIME NOT NULL
)�1�9tableSessionSessionCREATE TABLE "Session" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "sessionToken" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "expires" DATETIME NOT NULL,
    CONSTRAINT "Session_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User" ("id") ON DELETE CASCADE ON UPDATE CASCADE
)-A indexsqlite_autoindex_Session_1Session�r�;tableAccountAccountCREATE TABLE "Account" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "userId" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "provider" TEXT NOT NULL,
    "providerAccountId" TEXT NOT NULL,
    "refresh_token" TEXT,
    "access_token" TEXT,
    "expires_at" INTEGER,
    "token_type" TEXT,
    "scope" TEXT,
    "id_token" TEXT,
    "session_state" TEXT,
    CONSTRAINT "Account_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User" ("id") ON DELETE CASCADE ON UPDATE CASCADE
)-A indexsqlite_autoindex_Account_1Account�o##�%tableFreedomFundFreedomFundCREATE TABLE "FreedomFund" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "userId" TEXT NOT NULL,
    "monthlyExpenses" REAL NOT NULL,
    "coverageMonths" INTEGER NOT NULL,
    "targetSavings" REAL NOT NULL,
    "currentSavingsAmount" REAL,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    CONSTRAINT "FreedomFund_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
)5I# indexsqlite_autoindex_FreedomFund_1FreedomFund	�     3G! indexsqlite_autoindex_ForumReply_1ForumReply�z!!�?tableForumReplyForumReplyCREATE TABLE "ForumReply" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "content" TEXT NOT NULL,
    "authorId" TEXT NOT NULL,
    "postId" TEXT NOT NULL,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "ForumReply_authorId_fkey" FOREIGN KEY ("authorId") REFERENCES "User" ("id") ON DELETE RESTRICT ON UPDATE CASCADE,
    CONSTRAINT "ForumReply_postId_fkey" FOREIGN KEY ("postId") REFERENCES "ForumPost" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
)1E indexsqlite_autoindex_ForumPost_1ForumPost   ��3tableForumPostForumPostCREATE TABLE "ForumPost" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "title" TEXT NOT NULL,
    "content" TEXT NOT NULL,
    "authorId" TEX;!O) indexsqlite_autoindex_ResourceRating_1ResourceRating$�H55�H55�3tableUserLearningProgressUserLearningProgress!CREATE TABLE "UserLearningProgress" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "userId" TEXT NOT NULL,
    "resourceId" TEXT NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'NOT_STARTED',
    "completedAt" DATETIME,
    "notes" TEXT,
    "rating" INTEGER,
    "review" TEXT,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    CONSTRAINT "UserLearningProgress_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User" ("id") ON DELETE RESTRICT ON UPDATE CASCADE,
    CONSTRAINT "UserLearningProgress_resourceId_fkey" FOREIGN KEY ("resourceId") REFERENCES "LearningResource" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
)?S- indexsqlite_autoindex_LearningResource_1LearningResource �--�itableLearningResourceLearningResourceCREATE TABLE "LearningResource" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "title" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "url" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "category" TEXT NOT NULL,
    "skillLevel" TEXT NOT NULL,
    "author" TEXT,
    "duration" TEXT,
    "cost" TEXT NOT NULL DEFAULT 'FREE',
    "format" TEXT NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL
)��4   %
    a 
��H�0k�m ���� 
�
c�]�= a                                                 �
2C/�/indexVerificationToken_token_keyVerificationToken6CREATE UNIQUE INDEX "VerificationToken_token_key" ON "VerificationToken"("token")}'C�/indexUser_passwordResetToken_keyUser+CREATE UNIQUE INDEX "User_passwordResetToken_key" ON "User"("passwordResetToken")U&){indexUser_email_keyUser*CREATE UNIQUE INDEX "User_email_key" ON "User"("email")�$%==�[table_SkillToLearningResource_SkillToLearningResource)CREATE TABLE "_SkillToLearningResource" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL,
    CONSTRAINT "_SkillToLearningResource_A_fkey" FOREIGN KEY ("A") REFERENCES "LearningResource" ("id") ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT "_SkillToLearningResource_B_fkey" FOREIGN KEY ("B") REFERENCES "Skill" ("id") ON DELETE CASCADE ON UPDATE CASCADE
)�B$GG�table_CareerPathToLearningResource_CareerPathToLearningResource(CREATE TABLE "_CareerPathToLearningResource" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL,
    CONSTRAINT "_CareerPathToLearningResource_A_fkey" FOREIGN KEY ("A") REFERENCES "CareerPath" ("id") ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT "_CareerPathToLearningResource_B_fkey" FOREIGN KEY ("B") REFERENCES "LearningResource" ("id") ON DELETE CASCADE ON UPDATE CASCADE
)�#77�Ctable_CareerPathToIndustry_CareerPathToIndustry'CREATE TABLE "_CareerPathToIndustry" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL,
    CONSTRAINT "_CareerPathToIndustry_A_fkey" FOREIGN KEY ("A") REFERENCES "CareerPath" ("id") ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT "_CareerPathToIndustry_B_fkey" FOREIGN KEY ("B") REFERENCES "Industry" ("id") ON DELETE CASCADE ON UPDATE CASCADE
)� "11�+table_CareerPathToSkill_CareerPathToSkill&CREATE TABLE "_CareerPathToSkill" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL,
    CONSTRAINT "_CareerPathToSkill_A_fkey" FOREIGN KEY ("A") REFERENCES "CareerPath" ("id") ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT "_CareerPathToSkill_B_fkey" FOREIGN KEY ("B") REFERENCES "Skill" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);!O) indexsqlite_autoindex_ResourceRating_1ResourceRating$�l ))�tableResourceRatingResourceRating#CREATE TABLE "ResourceRating" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "userId" TEXT NOT NULL,
    "resourceId" TEXT NOT NULL,
    "rating" INTEGER NOT NULL,
    "review" TEXT,
    "isHelpful" BOOLEAN,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    CONSTRAINT "ResourceRating_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User" ("id") ON DELETE RESTRICT ON UPDATE CASCADE,
    CONSTRAINT "ResourceRating_resourceId_fkey" FOREIGN KEY ("resourceId") REFERENCES "LearningResource" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
)G[5 indexsqlite_autoindex_UserLearningProgress_1UserLearningProgress"w1=�#indexSession_sessionToken_keySession5CREATE UNIQUE INDEX "Session_sessionToken_key" ON "Session"("sessionToken")�$0Y�aindexAccount_provider_providerAccountId_keyAccount4CREATE UNIQUE INDEX "Account_provider_providerAccountId_key" ON "Account"("provider", "providerAccountId")u/9#�indexFreedomFund_userId_keyFreedomFund3CREATE UNIQUE INDEX "FreedomFund_userId_key" ON "FreedomFund"("userId")�.K)�1indexSuggestionRule_careerPathId_idxSuggestionRule2CREATE INDEX "SuggestionRule_careerPathId_idx" ON "SuggestionRule"("careerPathId")c-/�indexIndustry_name_keyIndustry1CREATE UNIQUE INDEX "Industry_name_key" ON "Industry"("name")V,){indexSkill_name_keySkill0CREATE UNIQUE INDEX "Skill_name_key" ON "Skill"("name")k+3!�indexCareerPath_slug_keyCareerPath/CREATE UNIQUE INDEX "CareerPath_slug_key" ON "CareerPath"("slug")k*3!�indexCareerPath_name_keyCareerPath.CREATE UNIQUE INDEX "CareerPath_name_key" ON "CareerPath"("name")�)S1�AindexAssessmentResponse_assessmentId_idxAssessmentResponse-CREATE INDEX "AssessmentResponse_assessmentId_idx" ON "AssessmentResponse"("assessmentId")e(1�indexProfile_userId_keyProfile,CREATE UNIQUE INDEX "Profile_userId_key" ON "Profile"("userId")��\