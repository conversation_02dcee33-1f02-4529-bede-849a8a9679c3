#!/bin/bash

# Comprehensive Test Execution Script
# This script runs all tests and generates reports

set -e

echo "🚀 Starting Comprehensive End-to-End Testing"
echo "=============================================="

# Check if Node.js and npm are available
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js first."
    exit 1
fi

if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed. Please install npm first."
    exit 1
fi

# Create test reports directory
mkdir -p test-reports

# Set environment variables for testing
export NODE_ENV=test
export TEST_DATABASE_URL="file:./test.db"
export NEXTAUTH_SECRET="test-secret"
export NEXTAUTH_URL="http://localhost:3000"

echo "📦 Installing dependencies..."
npm install

echo "🗄️ Setting up test database..."
npx prisma generate
npx prisma db push --force-reset

echo "🌱 Seeding test data..."
npm run prisma:seed

echo ""
echo "🧪 Running Comprehensive Test Suite"
echo "===================================="

# Track overall test results
OVERALL_SUCCESS=true

# Function to run individual test suite
run_test_suite() {
    local suite_name="$1"
    local test_command="$2"
    local description="$3"
    
    echo ""
    echo "📋 Running $suite_name Tests"
    echo "   $description"
    echo "----------------------------------------"
    
    if eval "$test_command"; then
        echo "✅ $suite_name Tests - PASSED"
    else
        echo "❌ $suite_name Tests - FAILED"
        OVERALL_SUCCESS=false
    fi
}

# Run individual test suites
run_test_suite "Authentication" "npm run test:auth" "User registration, login, and session management"
run_test_suite "Assessment" "npm run test:assessment" "Career assessment functionality"
run_test_suite "Learning Resources" "npm run test:resources" "Resource browsing, rating, and recommendations"
run_test_suite "Community Forum" "npm run test:forum" "Forum posting, commenting, and interactions"
run_test_suite "API Endpoints" "npm run test:api" "Backend API endpoint testing"
run_test_suite "UI Components" "npm run test:ui" "Frontend React component testing"
run_test_suite "Security" "npm run test:security" "Security vulnerability and protection testing"
run_test_suite "Performance" "npm run test:performance" "Performance and load testing"

echo ""
echo "📊 Generating Coverage Report..."
npm run test:coverage

echo ""
echo "📄 Generating Comprehensive Report..."
npm run test:comprehensive

echo ""
echo "=============================================="
echo "🏁 COMPREHENSIVE TESTING COMPLETE"
echo "=============================================="

if [ "$OVERALL_SUCCESS" = true ]; then
    echo "🎉 ALL TESTS PASSED! System is ready for production."
    echo ""
    echo "✅ Test Results Summary:"
    echo "   - Authentication: PASSED"
    echo "   - Assessment: PASSED"
    echo "   - Learning Resources: PASSED"
    echo "   - Community Forum: PASSED"
    echo "   - API Endpoints: PASSED"
    echo "   - UI Components: PASSED"
    echo "   - Security: PASSED"
    echo "   - Performance: PASSED"
    echo ""
    echo "🔒 Security: All security tests passed"
    echo "⚡ Performance: All performance benchmarks met"
    echo "🎯 Coverage: Comprehensive test coverage achieved"
    echo ""
    echo "📊 Reports Available:"
    echo "   - Comprehensive Report: test-reports/comprehensive-test-report.md"
    echo "   - Coverage Report: coverage/lcov-report/index.html"
    echo "   - Individual Test Reports: test-reports/*-test-report.md"
    echo ""
    echo "🚀 SYSTEM STATUS: PRODUCTION READY"
    exit 0
else
    echo "⚠️ Some tests failed. Review the reports for details."
    echo ""
    echo "❌ Test Results Summary:"
    echo "   Some test suites failed - see individual results above"
    echo ""
    echo "🔧 Next Steps:"
    echo "   1. Review failed test reports"
    echo "   2. Fix identified issues"
    echo "   3. Re-run tests"
    echo "   4. Ensure all tests pass before deployment"
    echo ""
    echo "📊 Reports Available:"
    echo "   - Comprehensive Report: test-reports/comprehensive-test-report.md"
    echo "   - Coverage Report: coverage/lcov-report/index.html"
    echo "   - Individual Test Reports: test-reports/*-test-report.md"
    echo ""
    echo "🚨 SYSTEM STATUS: NEEDS ATTENTION"
    exit 1
fi
