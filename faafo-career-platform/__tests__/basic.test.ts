/**
 * Basic Test Suite
 * Verifies that the testing environment is properly configured
 */

describe('Basic Testing Environment', () => {
  it('should run basic tests', () => {
    expect(1 + 1).toBe(2);
  });

  it('should support TypeScript', () => {
    const testObject: { name: string; value: number } = {
      name: 'test',
      value: 42
    };
    
    expect(testObject.name).toBe('test');
    expect(testObject.value).toBe(42);
  });

  it('should support async operations', async () => {
    const asyncFunction = async (): Promise<string> => {
      return new Promise((resolve) => {
        setTimeout(() => resolve('async result'), 10);
      });
    };

    const result = await asyncFunction();
    expect(result).toBe('async result');
  });

  it('should have environment variables', () => {
    expect(process.env.NODE_ENV).toBeDefined();
  });
});
