import Redis from 'ioredis';
import { apiCache } from '@/lib/cache';

// Enhanced cache interface for AI and advanced features
interface EnhancedCacheInterface {
  get(key: string): Promise<string | null>;
  set(key: string, value: string, ttlSeconds?: number): Promise<void>;
  delete(key: string): Promise<void>;
  clear(): Promise<void>;
  healthCheck(): Promise<boolean>;
  mget(keys: string[]): Promise<(string | null)[]>;
  mset(keyValues: Record<string, string>, ttlSeconds?: number): Promise<void>;
}

// Redis cache implementation for production
class RedisCache implements EnhancedCacheInterface {
  private redis: Redis;
  private isConnected: boolean = false;

  constructor() {
    const redisUrl = process.env.REDIS_URL || 'redis://localhost:6379';
    this.redis = new Redis(redisUrl, {
      retryDelayOnFailover: 100,
      maxRetriesPerRequest: 3,
      lazyConnect: true,
      connectTimeout: 10000,
      commandTimeout: 5000,
    });

    this.redis.on('connect', () => {
      this.isConnected = true;
      console.log('Redis connected successfully');
    });

    this.redis.on('error', (error) => {
      this.isConnected = false;
      console.error('Redis connection error:', error);
    });

    this.redis.on('close', () => {
      this.isConnected = false;
      console.warn('Redis connection closed');
    });
  }

  async get(key: string): Promise<string | null> {
    try {
      if (!this.isConnected) return null;
      return await this.redis.get(key);
    } catch (error) {
      console.error('Redis get error:', error);
      return null;
    }
  }

  async set(key: string, value: string, ttlSeconds: number = 3600): Promise<void> {
    try {
      if (!this.isConnected) return;
      await this.redis.setex(key, ttlSeconds, value);
    } catch (error) {
      console.error('Redis set error:', error);
    }
  }

  async delete(key: string): Promise<void> {
    try {
      if (!this.isConnected) return;
      await this.redis.del(key);
    } catch (error) {
      console.error('Redis delete error:', error);
    }
  }

  async clear(): Promise<void> {
    try {
      if (!this.isConnected) return;
      await this.redis.flushall();
    } catch (error) {
      console.error('Redis clear error:', error);
    }
  }

  async mget(keys: string[]): Promise<(string | null)[]> {
    try {
      if (!this.isConnected) return keys.map(() => null);
      return await this.redis.mget(...keys);
    } catch (error) {
      console.error('Redis mget error:', error);
      return keys.map(() => null);
    }
  }

  async mset(keyValues: Record<string, string>, ttlSeconds: number = 3600): Promise<void> {
    try {
      if (!this.isConnected) return;
      
      const pipeline = this.redis.pipeline();
      Object.entries(keyValues).forEach(([key, value]) => {
        pipeline.setex(key, ttlSeconds, value);
      });
      
      await pipeline.exec();
    } catch (error) {
      console.error('Redis mset error:', error);
    }
  }

  async healthCheck(): Promise<boolean> {
    try {
      const result = await this.redis.ping();
      return result === 'PONG';
    } catch {
      return false;
    }
  }
}

// Enhanced memory cache fallback
class EnhancedMemoryCache implements EnhancedCacheInterface {
  private cache = new Map<string, { value: string; expiry: number }>();
  private cleanupInterval: NodeJS.Timeout;

  constructor() {
    // Clean up expired items every 5 minutes
    this.cleanupInterval = setInterval(() => {
      this.cleanup();
    }, 5 * 60 * 1000);
  }

  async get(key: string): Promise<string | null> {
    const item = this.cache.get(key);
    if (!item) return null;

    if (Date.now() > item.expiry) {
      this.cache.delete(key);
      return null;
    }

    return item.value;
  }

  async set(key: string, value: string, ttlSeconds: number = 3600): Promise<void> {
    const expiry = Date.now() + (ttlSeconds * 1000);
    this.cache.set(key, { value, expiry });
  }

  async delete(key: string): Promise<void> {
    this.cache.delete(key);
  }

  async clear(): Promise<void> {
    this.cache.clear();
  }

  async mget(keys: string[]): Promise<(string | null)[]> {
    return Promise.resolve(keys.map(key => {
      const item = this.cache.get(key);
      if (!item || Date.now() > item.expiry) {
        this.cache.delete(key);
        return null;
      }
      return item.value;
    }));
  }

  async mset(keyValues: Record<string, string>, ttlSeconds: number = 3600): Promise<void> {
    const expiry = Date.now() + (ttlSeconds * 1000);
    Object.entries(keyValues).forEach(([key, value]) => {
      this.cache.set(key, { value, expiry });
    });
  }

  async healthCheck(): Promise<boolean> {
    return true;
  }

  private cleanup(): void {
    const now = Date.now();
    for (const [key, item] of this.cache.entries()) {
      if (now > item.expiry) {
        this.cache.delete(key);
      }
    }
  }

  destroy(): void {
    clearInterval(this.cleanupInterval);
    this.clear();
  }
}

// Cache factory - uses Redis in production, memory cache in development
function createEnhancedCache(): EnhancedCacheInterface {
  if (process.env.NODE_ENV === 'production' && process.env.REDIS_URL) {
    return new RedisCache();
  }
  return new EnhancedMemoryCache();
}

// Enhanced cache service with AI-specific utilities
class CacheService {
  private cache: EnhancedCacheInterface;

  constructor() {
    this.cache = createEnhancedCache();
  }

  // Basic cache operations
  async get(key: string): Promise<string | null> {
    return this.cache.get(key);
  }

  async set(key: string, value: string, ttlSeconds?: number): Promise<void> {
    return this.cache.set(key, value, ttlSeconds);
  }

  async delete(key: string): Promise<void> {
    return this.cache.delete(key);
  }

  async clear(): Promise<void> {
    return this.cache.clear();
  }

  // JSON operations
  async getJSON<T>(key: string): Promise<T | null> {
    const value = await this.cache.get(key);
    if (!value) return null;
    
    try {
      return JSON.parse(value);
    } catch {
      return null;
    }
  }

  async setJSON<T>(key: string, value: T, ttlSeconds?: number): Promise<void> {
    return this.cache.set(key, JSON.stringify(value), ttlSeconds);
  }

  // Batch operations
  async mget(keys: string[]): Promise<(string | null)[]> {
    return this.cache.mget(keys);
  }

  async mset(keyValues: Record<string, string>, ttlSeconds?: number): Promise<void> {
    return this.cache.mset(keyValues, ttlSeconds);
  }

  // AI-specific cache utilities
  generateAIKey(type: string, userId: string, ...params: string[]): string {
    return `ai:${type}:${userId}:${params.join(':')}`;
  }

  async cacheAIResponse(
    type: string,
    userId: string,
    params: string[],
    response: any,
    ttlSeconds: number = 3600
  ): Promise<void> {
    const key = this.generateAIKey(type, userId, ...params);
    await this.setJSON(key, response, ttlSeconds);
  }

  async getAIResponse<T>(
    type: string,
    userId: string,
    params: string[]
  ): Promise<T | null> {
    const key = this.generateAIKey(type, userId, ...params);
    return this.getJSON<T>(key);
  }

  // Cache with fallback to existing memory cache
  async getWithFallback<T>(key: string): Promise<T | null> {
    // Try enhanced cache first
    const enhanced = await this.getJSON<T>(key);
    if (enhanced) return enhanced;

    // Fallback to existing memory cache
    const fallback = apiCache.get<T>(key);
    if (fallback) {
      // Migrate to enhanced cache
      await this.setJSON(key, fallback);
      return fallback;
    }

    return null;
  }

  async setWithFallback<T>(key: string, value: T, ttlSeconds?: number): Promise<void> {
    // Set in both caches
    await this.setJSON(key, value, ttlSeconds);
    apiCache.set(key, value, ttlSeconds ? ttlSeconds * 1000 : undefined);
  }

  // Health check
  async healthCheck(): Promise<boolean> {
    return this.cache.healthCheck();
  }

  // Cache statistics
  async getStats(): Promise<{ type: string; healthy: boolean; [key: string]: any }> {
    const healthy = await this.healthCheck();
    
    if (this.cache instanceof RedisCache) {
      return {
        type: 'redis',
        healthy,
        connected: (this.cache as any).isConnected,
      };
    } else {
      return {
        type: 'memory',
        healthy,
        size: (this.cache as any).cache?.size || 0,
      };
    }
  }
}

// Export singleton instance
export const cacheService = new CacheService();

// Cache key generators for AI features
export const aiCacheKeys = {
  resumeAnalysis: (userId: string, resumeHash: string) => 
    cacheService.generateAIKey('resume_analysis', userId, resumeHash),
  
  careerRecommendations: (userId: string, assessmentId: string) => 
    cacheService.generateAIKey('career_recommendations', userId, assessmentId),
  
  skillsAnalysis: (userId: string, careerPath: string) => 
    cacheService.generateAIKey('skills_analysis', userId, careerPath.replace(/\s+/g, '_')),
  
  interviewPrep: (userId: string, careerPath: string, companyType: string) => 
    cacheService.generateAIKey('interview_prep', userId, careerPath.replace(/\s+/g, '_'), companyType),
  
  personalizedContent: (userId: string, contentType: string, timestamp: string) => 
    cacheService.generateAIKey('personalized_content', userId, contentType, timestamp),
};

export default cacheService;
